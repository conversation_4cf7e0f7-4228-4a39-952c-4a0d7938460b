package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import com.sanythadmin.project.workstudy.enums.AdjustmentStatus;
import com.sanythadmin.project.workstudy.enums.WorkStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学生岗位申请实体
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_STUDENT_APPLY")
@Entity
@Table(name = "SYT_QGZX_STUDENT_APPLY")
public class QgzxStudentApply extends BaseApplicationInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 学号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 岗位ID
     */
    @Column(name = "JOB_ID")
    @TableField("JOB_ID")
    private String jobId;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ")
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 申请时间
     */
    @Column(name = "SQSJ")
    @TableField("SQSJ")
    private LocalDateTime sqsj;

    /**
     * 申请理由
     */
    @Column(name = "SQLY")
    @TableField("sqly")
    private String sqly;
    /**
     * 特长优势
     */
    @Column(name = "TCYS")
    @TableField("TCYS")
    private String tcys;
    /**
     * 是否服从安排
     */
    @Column(name = "SFFCAP",columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFFCAP")
    private JudgeMark sffcap;

    /**
     * 审核状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "SPZT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SPZT")
    private ReviewResult spzt;

    /**
     * 用工状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "YGZT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("YGZT")
    private WorkStatus ygzt;

    /**
     * 用工开始日期
     */
    @Column(name = "YGKSRQ")
    @TableField("YGKSRQ")
    private LocalDate ygksrq;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID")
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 调剂状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "TJZT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TJZT")
    private AdjustmentStatus tjzt;

    /**
     * 原岗位ID（调剂前的岗位ID）
     */
    @Column(name = "ORIGINAL_JOB_ID")
    @TableField("ORIGINAL_JOB_ID")
    private String originalJobId;

    /**
     * 调剂申请时间
     */
    @Column(name = "TJSQSJ")
    @TableField("TJSQSJ")
    private LocalDateTime tjsqsj;

    /**
     * 调剂确认时间（用人单位确认接收时间）
     */
    @Column(name = "TJQRSJ")
    @TableField("TJQRSJ")
    private LocalDateTime tjqrsj;

    /**
     * 学生确认调剂时间
     */
    @Column(name = "XSQRSJ")
    @TableField("XSQRSJ")
    private LocalDateTime xsqrsj;

    /**
     * 岗位信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxJobApplication jobApplication;

    /**
     * 原岗位信息（调剂前的岗位信息）
     */
    @Transient
    @TableField(exist = false)
    private QgzxJobApplication originalJobApplication;

    /**
     * 学生信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;

}